# Building a Neural Network for MNIST Digit Classification with PyTorch

## Introduction

In this comprehensive tutorial, we'll explore how to build a deep neural network from scratch using PyTorch to classify handwritten digits from the famous MNIST dataset. This project demonstrates fundamental concepts in deep learning, including neural network architecture design, training loops, and model evaluation.

**📖 Chinese Version Available**: A detailed Chinese version of this README with beginner-friendly explanations is also provided as `README_Chinese.md` for Chinese-speaking learners.

The MNIST dataset consists of 70,000 grayscale images of handwritten digits (0-9), each 28×28 pixels in size. It's considered the "Hello World" of computer vision and serves as an excellent starting point for understanding neural networks.

## Table of Contents

1. [Project Overview](#project-overview)
2. [Neural Network Architecture](#neural-network-architecture)
3. [Data Loading and Preprocessing](#data-loading-and-preprocessing)
4. [Model Implementation](#model-implementation)
5. [Training Process](#training-process)
6. [Evaluation and Visualization](#evaluation-and-visualization)
7. [Results and Analysis](#results-and-analysis)
8. [Key Concepts Explained](#key-concepts-explained)
9. [Code Walkthrough](#code-walkthrough)
10. [Conclusion](#conclusion)

## Project Overview

Our neural network implementation includes:
- **Multi-layer Perceptron (MLP)** with 4 fully connected layers
- **Linear transformations** without activation functions (simplified version)
- **Adam optimizer** for efficient gradient descent
- **Cross-entropy loss** for multi-class classification
- **Batch processing** for efficient training
- **GPU acceleration** support
- **Sample visualization** of predictions

## Neural Network Architecture

### Architecture Design

Our neural network follows a simplified feedforward architecture:

```
Input Layer (784 neurons) → Flatten 28×28 images
Hidden Layer 1 (128 neurons) → Linear transformation
Hidden Layer 2 (128 neurons) → Linear transformation
Hidden Layer 3 (64 neurons) → Linear transformation
Output Layer (10 neurons) → Softmax (implicit in CrossEntropyLoss)
```

### Why This Architecture?

1. **Input Flattening**: The 28×28 pixel images are flattened into 784-dimensional vectors
2. **Consistent Hidden Layers**: Two 128-neuron layers for feature extraction and combination
3. **Dimensionality Reduction**: Gradual reduction from 128 to 64 to 10 neurons
4. **Linear Transformations**: Simplified version without activation functions for educational purposes
5. **Output Layer**: 10 neurons correspond to 10 digit classes (0-9)

## Data Loading and Preprocessing

### MNIST Dataset Characteristics

- **Training Set**: 60,000 images
- **Test Set**: 10,000 images
- **Image Size**: 28×28 pixels (grayscale)
- **Classes**: 10 digits (0-9)
- **Pixel Values**: 0-255 (normalized to 0-1)

### Preprocessing Pipeline

```python
transform=ToTensor()  # Converts PIL images to tensors and normalizes [0,1]
```

The `ToTensor()` transform:
1. Converts PIL Image or numpy array to PyTorch tensor
2. Scales pixel values from [0, 255] to [0.0, 1.0]
3. Changes data layout from HWC to CHW format

## Model Implementation

### Class Structure

Our `NeuralNetwork` class inherits from `nn.Module`, PyTorch's base class for neural networks:

```python
class NeuralNetwork(nn.Module):
    def __init__(self):
        super(NeuralNetwork, self).__init__()
        self.flatten = nn.Flatten()
        self.fc1 = nn.Linear(28*28, 128)
        self.fc2 = nn.Linear(128, 128)
        self.fc3 = nn.Linear(128, 64)
        self.fc4 = nn.Linear(64, 10)

    def forward(self, x):
        x = self.flatten(x)
        x = self.fc1(x)
        x = self.fc2(x)
        x = self.fc3(x)
        x = self.fc4(x)
        return x
```

### Layer-by-Layer Breakdown

1. **nn.Flatten()**: Reshapes input from (batch_size, 1, 28, 28) to (batch_size, 784)
2. **nn.Linear(784, 128)**: First fully connected layer (fc1)
3. **nn.Linear(128, 128)**: Second fully connected layer (fc2)
4. **nn.Linear(128, 64)**: Third fully connected layer (fc3)
5. **nn.Linear(64, 10)**: Output layer (fc4) - maps to 10 output classes

## Training Process

### Training Loop Components

#### Forward Pass
1. **Input Processing**: Batch of images fed through network
2. **Prediction Generation**: Model outputs logits for each class
3. **Loss Calculation**: Cross-entropy loss between predictions and true labels

#### Backward Pass
1. **Gradient Computation**: `loss.backward()` computes gradients
2. **Parameter Update**: `optimizer.step()` updates weights
3. **Gradient Reset**: `optimizer.zero_grad()` clears gradients

### Key Training Parameters

- **Batch Size**: 64 (good balance between memory and convergence)
- **Learning Rate**: 0.0045 (tuned for Adam optimizer)
- **Epochs**: 25 (sufficient for convergence on MNIST)
- **Optimizer**: Adam (adaptive learning rate with momentum)

## Evaluation and Visualization

### Model Evaluation

The test function provides comprehensive evaluation:
- **Accuracy Calculation**: Percentage of correctly classified images
- **Loss Tracking**: Average cross-entropy loss on test set
- **Visual Inspection**: Sample predictions with true/predicted labels

### Visualization Features

1. **Sample Predictions**: Shows 10 random test images with predictions after training
2. **Training Loss Curve**: Plots training loss over epochs after training completion
3. **Real-time Feedback**: Displays training progress every 100 batches

## Results and Analysis

### Expected Performance

With this simplified architecture and hyperparameters, you can expect:
- **Training Accuracy**: ~95-98%
- **Test Accuracy**: ~95-97%
- **Training Time**: 2-3 minutes on GPU, 10-15 minutes on CPU
- **Convergence**: Typically within 20-25 epochs

### Performance Factors

1. **Architecture Simplicity**: Linear layers without activation functions
2. **Network Depth**: 4-layer structure provides sufficient complexity for MNIST
3. **Adam Optimizer**: Adaptive learning rates improve convergence
4. **Batch Processing**: Stable gradient estimates

## Key Concepts Explained

### Cross-Entropy Loss

Cross-entropy loss is ideal for multi-class classification:
```
Loss = -Σ(y_true * log(y_pred))
```
- Penalizes confident wrong predictions heavily
- Provides strong gradients for learning
- Works well with softmax output

### Adam Optimizer

Adam combines advantages of AdaGrad and RMSprop:
- **Adaptive Learning Rates**: Different rates for each parameter
- **Momentum**: Accelerates convergence in consistent directions
- **Bias Correction**: Accounts for initialization bias

### Batch Processing

Processing data in batches provides:
- **Memory Efficiency**: Fits large datasets in limited memory
- **Stable Gradients**: Reduces noise in gradient estimates
- **Parallelization**: Leverages GPU/CPU parallel processing

## Detailed Code Walkthrough

### 1. Import Statements and Dependencies

```python
import torch
from torch import nn
from torch.utils.data import DataLoader
from torchvision import datasets
from torchvision.transforms import ToTensor
import matplotlib.pyplot as plt
import numpy as np
```

**Explanation:**
- `torch`: Core PyTorch library for tensor operations and neural networks
- `torch.nn`: Contains neural network layers, loss functions, and utilities
- `DataLoader`: Handles batch processing and data iteration
- `torchvision.datasets`: Provides access to common datasets like MNIST
- `ToTensor()`: Transform that converts PIL images to PyTorch tensors
- `matplotlib.pyplot`: For visualization and plotting
- `numpy`: For numerical operations and array handling

### 2. Data Loading and Preparation

```python
# Download and prepare training data
training_data = datasets.MNIST(
    root="data",             # Directory to store dataset
    train=True,              # Load training split
    download=True,           # Download if not present
    transform=ToTensor(),    # Convert to tensor and normalize
)

# Download and prepare test data
test_data = datasets.MNIST(
    root="data",
    train=False,             # Load test split
    download=True,
    transform=ToTensor(),
)
```

**Detailed Explanation:**
- **root="data"**: Creates a local 'data' folder to store the dataset
- **train=True/False**: Specifies whether to load training (60,000 images) or test (10,000 images) set
- **download=True**: Automatically downloads MNIST if not already present
- **transform=ToTensor()**: Applies preprocessing:
  - Converts PIL Image (0-255) to PyTorch tensor (0.0-1.0)
  - Changes format from HWC (Height, Width, Channels) to CHW
  - Ensures data type compatibility with neural network

### 3. Data Visualization Function

```python
def show_samples(dataset, model, n_samples=10):
    fig, axes = plt.subplots(1, n_samples, figsize=(15,3))
    indices = np.random.choice(len(dataset), n_samples)

    for i, idx in enumerate(indices):
        img, label = dataset[idx]
        with torch.no_grad():
            pred = model(img.unsqueeze(0).to(device))
            pred_label = pred.argmax().item()

        axes[i].imshow(img.squeeze(), cmap='gray')
        axes[i].set_title(f"True: {label}\nPred: {pred_label}")
        axes[i].axis('off')

    plt.tight_layout()
    plt.show()
```

**Line-by-Line Analysis:**
- **Line 2**: Creates subplot grid (1 row, n_samples columns)
- **Line 3**: Randomly selects sample indices from dataset
- **Line 6**: Gets image and true label from dataset
- **Line 7**: `torch.no_grad()` disables gradient computation (saves memory during inference)
- **Line 8**:
  - `img.unsqueeze(0)` adds batch dimension: (28,28) → (1,28,28)
  - `.to(device)` moves tensor to GPU/CPU
  - `model(...)` performs forward pass
- **Line 9**: `argmax()` finds class with highest probability, `.item()` converts to Python int
- **Line 11**: `img.squeeze()` removes single dimensions for display
- **Line 12**: Shows true label and predicted label

### 4. DataLoader Creation

```python
train_dataloader = DataLoader(training_data, batch_size=64)
test_dataloader = DataLoader(test_data, batch_size=64)

# Inspect data shapes
for x, y in train_dataloader:
    print(f"shape of x[N,C,H,W]:{x.shape}")    # Image batch shape
    print(f"shape of y:{y.shape,y.dtype}")     # Label batch shape
    break
```

**Explanation:**
- **DataLoader**: Creates iterable batches from dataset
- **batch_size=64**: Processes 64 images simultaneously
  - Larger batches: More stable gradients, better GPU utilization
  - Smaller batches: More frequent updates, less memory usage
- **Shape Analysis**:
  - `x.shape`: (64, 1, 28, 28) = (batch_size, channels, height, width)
  - `y.shape`: (64,) = batch of 64 labels
  - `y.dtype`: torch.int64 (suitable for classification)

### 5. Device Configuration

```python
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using {device} device")
```

**Purpose:**
- **GPU Acceleration**: CUDA enables parallel processing on NVIDIA GPUs
- **Fallback**: Uses CPU if GPU unavailable
- **Performance Impact**: GPU can be 10-100x faster for neural network training

### 6. Neural Network Architecture

```python
class NeuralNetwork(nn.Module):
    def __init__(self):
        super(NeuralNetwork, self).__init__()
        self.flatten = nn.Flatten()
        self.fc1 = nn.Linear(28*28, 128)
        self.fc2 = nn.Linear(128, 128)
        self.fc3 = nn.Linear(128, 64)
        self.fc4 = nn.Linear(64, 10)

    def forward(self, x):
        x = self.flatten(x)
        x = self.fc1(x)
        x = self.fc2(x)
        x = self.fc3(x)
        x = self.fc4(x)
        return x
```

**Detailed Architecture Analysis:**

1. **nn.Flatten()**:
   - Converts 2D images to 1D vectors
   - Input: (batch_size, 1, 28, 28)
   - Output: (batch_size, 784)
   - Preserves batch dimension

2. **nn.Linear(784, 128)** (fc1):
   - First fully connected layer with weight matrix W(784×128) and bias b(128)
   - Computation: output = input × W + b
   - Parameters: 784 × 128 + 128 = 100,480

3. **nn.Linear(128, 128)** (fc2):
   - Second fully connected layer
   - Parameters: 128 × 128 + 128 = 16,512

4. **nn.Linear(128, 64)** (fc3):
   - Third fully connected layer
   - Parameters: 128 × 64 + 64 = 8,256

5. **nn.Linear(64, 10)** (fc4):
   - Output layer for 10 digit classes
   - Parameters: 64 × 10 + 10 = 650

6. **Total Parameters**: ~125,898 trainable parameters

**Note**: This implementation uses only linear transformations without activation functions, making it a simplified version suitable for educational purposes.

### 7. Training Function Implementation

```python
def train(dataloader, model, loss_fn, optimizer):
    model.train()                    # Set model to training mode
    epoch_loss = 0
    batch_size_num = 1

    for x, y in dataloader:
        x, y = x.to(device), y.to(device)   # Move data to GPU/CPU

        # Forward pass
        pred = model.forward(x)              # Get predictions
        loss = loss_fn(pred, y)              # Calculate loss

        # Backward pass
        optimizer.zero_grad()                # Clear previous gradients
        loss.backward()                      # Compute gradients
        optimizer.step()                     # Update parameters

        # Logging and tracking
        loss_value = loss.item()
        epoch_loss += loss_value
        if batch_size_num % 100 == 0:
            print(f"loss:{loss_value:>7f}    [number:{batch_size_num}]")
        batch_size_num += 1

    # Calculate average loss for epoch
    avg_loss = epoch_loss / len(dataloader)
    train_loss_history.append(avg_loss)
    return avg_loss
```

**Step-by-Step Training Process:**

1. **model.train()**: Enables training mode
   - Activates dropout (if present)
   - Enables batch normalization training behavior

2. **Data Movement**: `x.to(device), y.to(device)`
   - Transfers tensors to GPU for acceleration
   - Essential for GPU training

3. **Forward Pass**: `pred = model.forward(x)`
   - Input flows through all layers
   - Produces logits (raw scores) for each class

4. **Loss Calculation**: `loss = loss_fn(pred, y)`
   - Cross-entropy loss compares predictions with true labels
   - Higher loss indicates worse performance

5. **Gradient Reset**: `optimizer.zero_grad()`
   - PyTorch accumulates gradients by default
   - Must clear before each backward pass

6. **Backward Pass**: `loss.backward()`
   - Computes gradients using chain rule
   - Gradients stored in parameter.grad attributes

7. **Parameter Update**: `optimizer.step()`
   - Updates weights using computed gradients
   - Adam optimizer applies adaptive learning rates

8. **Progress Tracking**:
   - Records loss every 100 batches
   - Calculates epoch average loss
   - Stores in history for plotting

### 8. Testing Function Implementation

```python
def test(dataloader, model, loss_fn):
    size = len(dataloader.dataset)           # Total number of test samples
    num_batches = len(dataloader)            # Number of batches
    model.eval()                             # Set to evaluation mode
    test_loss, correct = 0, 0

    with torch.no_grad():                    # Disable gradient computation
        for x, y in dataloader:
            x, y = x.to(device), y.to(device)
            pred = model.forward(x)          # Forward pass
            test_loss += loss_fn(pred, y).item()  # Accumulate loss
            correct += (pred.argmax(1) == y).type(torch.float).sum().item()

    # Calculate metrics
    test_loss /= num_batches                 # Average loss
    correct /= size                          # Accuracy percentage
    test_loss_history.append(test_loss)

    print(f"\nTest Error:\n")
    print(f"Accuracy: {(100*correct)}%")
    print(f"Avg loss: {test_loss}")
    show_samples(test_data, model, n_samples=10)
    return test_loss, correct
```

**Evaluation Process Breakdown:**

1. **model.eval()**: Sets evaluation mode
   - Disables dropout
   - Uses population statistics for batch normalization

2. **torch.no_grad()**: Context manager that:
   - Disables gradient computation
   - Reduces memory usage
   - Speeds up inference

3. **Accuracy Calculation**:
   - `pred.argmax(1)`: Gets predicted class (highest logit)
   - `== y`: Compares with true labels
   - `.type(torch.float).sum()`: Counts correct predictions
   - `.item()`: Converts to Python number

4. **Metrics Computation**:
   - Average loss across all batches
   - Accuracy as percentage of correct predictions

### 9. Training Configuration and Execution

```python
loss_fn = nn.CrossEntropyLoss()                    # Loss function
optimizer = torch.optim.Adam(model.parameters(), lr=0.0045)  # Optimizer

if __name__ == "__main__":
    print(len(training_data))    # 60,000 training samples
    print(len(test_data))        # 10,000 test samples

    epochs = 25
    for i in range(epochs):
        print(f"\nEpoch {i+1}")
        train(train_dataloader, model, loss_fn, optimizer)

    # Plot training curve after all epochs
    plt.figure(figsize=(10,5))
    plt.plot(train_loss_history, label='Train Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training Loss')
    plt.legend()
    plt.grid()
    plt.show()

    # Test the model after training
    test(test_dataloader, model, loss_fn)
```

**Configuration Details:**

1. **CrossEntropyLoss**:
   - Combines LogSoftmax and NLLLoss
   - Suitable for multi-class classification
   - Provides strong gradients for learning

2. **Adam Optimizer**:
   - Learning rate: 0.0045 (tuned for this problem)
   - Adaptive learning rates per parameter
   - Built-in momentum and bias correction

3. **Training Loop**:
   - 25 epochs (complete passes through training data)
   - Each epoch processes all 60,000 training samples
   - Progress printed for each epoch

4. **Visualization**:
   - Plots training loss over epochs
   - Helps identify convergence and overfitting
   - Grid and labels for clarity

## Conclusion

This implementation demonstrates fundamental deep learning concepts through a practical MNIST classification task. The multi-layer perceptron architecture, while simple, effectively captures the patterns needed for digit recognition.

### Key Takeaways

1. **Architecture Matters**: Deep networks can learn complex hierarchical features
2. **Proper Training**: Careful selection of loss function, optimizer, and hyperparameters
3. **Evaluation**: Comprehensive testing ensures model generalization
4. **Visualization**: Understanding model behavior through visual inspection

### Next Steps

To extend this project, consider:
- Implementing Convolutional Neural Networks (CNNs)
- Adding regularization techniques (dropout, batch normalization)
- Experimenting with different optimizers and learning rates
- Applying to more complex datasets (CIFAR-10, ImageNet)

This foundation provides the building blocks for more advanced deep learning projects and computer vision applications.

---

**Author**: [xiaoze]
**Date**: [2025-07-13]
